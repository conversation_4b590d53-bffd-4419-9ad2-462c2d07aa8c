#!/bin/bash

# P<PERSON>ej<PERSON>t do Laravel adresáře
cd /var/www/html/backend/

# Počkat na připojení k databázi
echo "Laravel Scheduler: Čekám na databázi..."
until php artisan migrate:status > /dev/null 2>&1; do
    echo "Laravel Scheduler: <PERSON>b<PERSON><PERSON> ješt<PERSON> ne<PERSON>, ček<PERSON><PERSON>..."
    sleep 5
done

echo "Laravel Scheduler: Spouštím scheduler..."
php artisan schedule:work --verbose