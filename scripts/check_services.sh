#!/bin/bash

echo "=== KONTROLA LARAVEL SLUŽEB ==="
echo "Datum: $(date)"
echo ""

echo "1. Kontrola běžících PHP procesů:"
ps aux | grep php | grep -v grep
echo ""

echo "2. Kontrola Laravel Queue procesů:"
ps aux | grep "queue:listen" | grep -v grep
if [ $? -eq 0 ]; then
    echo "✅ Laravel Queue běží"
else
    echo "❌ Laravel Queue neběží"
fi
echo ""

echo "3. Kontrola Laravel Scheduler procesů:"
ps aux | grep "schedule:work" | grep -v grep
if [ $? -eq 0 ]; then
    echo "✅ Laravel Scheduler běží"
else
    echo "❌ Laravel Scheduler neběží"
fi
echo ""

echo "4. Kontrola Apache procesů:"
ps aux | grep apache2 | grep -v grep
if [ $? -eq 0 ]; then
    echo "✅ Apache běží"
else
    echo "❌ Apache neběží"
fi
echo ""

echo "5. Kontrola cron procesů:"
ps aux | grep cron | grep -v grep
if [ $? -eq 0 ]; then
    echo "✅ Cron běží"
else
    echo "❌ Cron neběží"
fi
echo ""

echo "6. Využití paměti:"
free -h
echo ""

echo "7. Posledních 10 řádků Laravel logů:"
if [ -f "/var/www/html/backend/storage/logs/laravel.log" ]; then
    tail -10 /var/www/html/backend/storage/logs/laravel.log
else
    echo "Laravel log soubor nenalezen"
fi
