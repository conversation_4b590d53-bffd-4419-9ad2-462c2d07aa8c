#!/bin/bash

# Přej<PERSON>t do Laravel adresáře
cd /var/www/html/backend

# Počkat na připojení k databázi
echo "Laravel Queue: Čekám na databázi..."
until php artisan migrate:status > /dev/null 2>&1; do
    echo "Laravel Queue: Datab<PERSON><PERSON> je<PERSON>t<PERSON>, <PERSON><PERSON><PERSON><PERSON>..."
    sleep 5
done

echo "Laravel Queue: Spouštím queue worker..."
php artisan queue:listen --queue=high,medium,low,default,sms --verbose --tries=3 --timeout=90
