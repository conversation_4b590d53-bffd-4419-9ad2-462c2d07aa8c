#!/bin/bash

# Nastavit práva pro skripty
if [ -d "/var/www/scripts" ]; then
    chmod +x /var/www/scripts/*.sh || true
fi

chmod +x /etc/cron.daily/db_backup

chown -R www-data:www-data /var/www/scripts/laravel_queue.sh
chown -R www-data:www-data /var/www/scripts/laravel_scheduler.sh

# Spustit cron
service cron start

# Počkat na databázi (volitelné, ale doporučené)
echo "Čekám na databázi..."
sleep 10

# Spustit Laravel Queue na pozadí jako www-data
echo "Spouštím Laravel Queue..."
su -s /bin/bash www-data -c '/var/www/scripts/laravel_queue.sh' &

# Spustit Laravel Scheduler na pozadí jako www-data
echo "Spouštím Laravel Scheduler..."
su -s /bin/bash www-data -c '/var/www/scripts/laravel_scheduler.sh' &

# Spustit Apache na popředí
echo "Spouštím Apache..."
exec apache2-foreground