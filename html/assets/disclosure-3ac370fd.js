import{u as P,o as d,c as K,l as D,H as y,t as O,p as T,N as k,a as v}from"./hidden-0aacd91d.js";import{b as M}from"./use-resolve-button-type-0913c19f.js";import{I as S,a as c,J as h,K as m,D as I,L as w,M as H,j as B}from"./index-04cb9130.js";var L=(l=>(l[l.Open=0]="Open",l[l.Closed=1]="Closed",l))(L||{});let C=Symbol("DisclosureContext");function g(l){let u=B(C,null);if(u===null){let r=new Error(`<${l} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,g),r}return u}let E=Symbol("DisclosurePanelContext");function J(){return B(E,null)}let U=S({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(l,{slots:u,attrs:r}){let o=c(l.defaultOpen?0:1),e=c(null),i=c(null),s={buttonId:c(null),panelId:c(null),disclosureState:o,panel:e,button:i,toggleDisclosure(){o.value=P(o.value,{[0]:1,[1]:0})},closeDisclosure(){o.value!==1&&(o.value=1)},close(t){s.closeDisclosure();let n=(()=>t?t instanceof HTMLElement?t:t.value instanceof HTMLElement?d(t):d(s.button):d(s.button))();n==null||n.focus()}};return h(C,s),K(m(()=>P(o.value,{[0]:D.Open,[1]:D.Closed}))),()=>{let{defaultOpen:t,...n}=l,p={open:o.value===0,close:s.close};return y({theirProps:n,ourProps:{},slot:p,slots:u,attrs:r,name:"Disclosure"})}}}),V=S({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:()=>`headlessui-disclosure-button-${O()}`}},setup(l,{attrs:u,slots:r,expose:o}){let e=g("DisclosureButton");I(()=>{e.buttonId.value=l.id}),w(()=>{e.buttonId.value=null});let i=J(),s=m(()=>i===null?!1:i.value===e.panelId.value),t=c(null);o({el:t,$el:t}),s.value||H(()=>{e.button.value=t.value});let n=M(m(()=>({as:l.as,type:u.type})),t);function p(){var a;l.disabled||(s.value?(e.toggleDisclosure(),(a=d(e.button))==null||a.focus()):e.toggleDisclosure())}function f(a){var b;if(!l.disabled)if(s.value)switch(a.key){case v.Space:case v.Enter:a.preventDefault(),a.stopPropagation(),e.toggleDisclosure(),(b=d(e.button))==null||b.focus();break}else switch(a.key){case v.Space:case v.Enter:a.preventDefault(),a.stopPropagation(),e.toggleDisclosure();break}}function $(a){switch(a.key){case v.Space:a.preventDefault();break}}return()=>{let a={open:e.disclosureState.value===0},{id:b,...x}=l,j=s.value?{ref:t,type:n.value,onClick:p,onKeydown:f}:{id:b,ref:t,type:n.value,"aria-expanded":l.disabled?void 0:e.disclosureState.value===0,"aria-controls":d(e.panel)?e.panelId.value:void 0,disabled:l.disabled?!0:void 0,onClick:p,onKeydown:f,onKeyup:$};return y({ourProps:j,theirProps:x,slot:a,attrs:u,slots:r,name:"DisclosureButton"})}}}),X=S({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:()=>`headlessui-disclosure-panel-${O()}`}},setup(l,{attrs:u,slots:r,expose:o}){let e=g("DisclosurePanel");I(()=>{e.panelId.value=l.id}),w(()=>{e.panelId.value=null}),o({el:e.panel,$el:e.panel}),h(E,e.panelId);let i=T(),s=m(()=>i!==null?(i.value&D.Open)===D.Open:e.disclosureState.value===0);return()=>{let t={open:e.disclosureState.value===0,close:e.close},{id:n,...p}=l,f={id:n,ref:e.panel};return y({ourProps:f,theirProps:p,slot:t,attrs:u,slots:r,features:k.RenderStrategy|k.Static,visible:s.value,name:"DisclosurePanel"})}}});export{U as Q,V,X};
