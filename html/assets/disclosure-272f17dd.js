import{u as P,o as d,c as K,l as m,H as y,t as O,p as T,N as k,a as v}from"./hidden-f039557c.js";import{b as H}from"./use-resolve-button-type-15920ae7.js";import{G as S,a as c,H as h,I as D,B as I,J as B,K as M,j as w}from"./index-ad469968.js";var L=(l=>(l[l.Open=0]="Open",l[l.Closed=1]="Closed",l))(L||{});let C=Symbol("DisclosureContext");function g(l){let u=w(C,null);if(u===null){let r=new Error(`<${l} /> is missing a parent <Disclosure /> component.`);throw Error.captureStackTrace&&Error.captureStackTrace(r,g),r}return u}let E=Symbol("DisclosurePanelContext");function G(){return w(E,null)}let R=S({name:"Disclosure",props:{as:{type:[Object,String],default:"template"},defaultOpen:{type:[Boolean],default:!1}},setup(l,{slots:u,attrs:r}){let o=c(l.defaultOpen?0:1),e=c(null),i=c(null),s={buttonId:c(null),panelId:c(null),disclosureState:o,panel:e,button:i,toggleDisclosure(){o.value=P(o.value,{[0]:1,[1]:0})},closeDisclosure(){o.value!==1&&(o.value=1)},close(t){s.closeDisclosure();let n=(()=>t?t instanceof HTMLElement?t:t.value instanceof HTMLElement?d(t):d(s.button):d(s.button))();n==null||n.focus()}};return h(C,s),K(D(()=>P(o.value,{[0]:m.Open,[1]:m.Closed}))),()=>{let{defaultOpen:t,...n}=l,p={open:o.value===0,close:s.close};return y({theirProps:n,ourProps:{},slot:p,slots:u,attrs:r,name:"Disclosure"})}}}),U=S({name:"DisclosureButton",props:{as:{type:[Object,String],default:"button"},disabled:{type:[Boolean],default:!1},id:{type:String,default:()=>`headlessui-disclosure-button-${O()}`}},setup(l,{attrs:u,slots:r,expose:o}){let e=g("DisclosureButton");I(()=>{e.buttonId.value=l.id}),B(()=>{e.buttonId.value=null});let i=G(),s=D(()=>i===null?!1:i.value===e.panelId.value),t=c(null);o({el:t,$el:t}),s.value||M(()=>{e.button.value=t.value});let n=H(D(()=>({as:l.as,type:u.type})),t);function p(){var a;l.disabled||(s.value?(e.toggleDisclosure(),(a=d(e.button))==null||a.focus()):e.toggleDisclosure())}function f(a){var b;if(!l.disabled)if(s.value)switch(a.key){case v.Space:case v.Enter:a.preventDefault(),a.stopPropagation(),e.toggleDisclosure(),(b=d(e.button))==null||b.focus();break}else switch(a.key){case v.Space:case v.Enter:a.preventDefault(),a.stopPropagation(),e.toggleDisclosure();break}}function $(a){switch(a.key){case v.Space:a.preventDefault();break}}return()=>{let a={open:e.disclosureState.value===0},{id:b,...x}=l,j=s.value?{ref:t,type:n.value,onClick:p,onKeydown:f}:{id:b,ref:t,type:n.value,"aria-expanded":l.disabled?void 0:e.disclosureState.value===0,"aria-controls":d(e.panel)?e.panelId.value:void 0,disabled:l.disabled?!0:void 0,onClick:p,onKeydown:f,onKeyup:$};return y({ourProps:j,theirProps:x,slot:a,attrs:u,slots:r,name:"DisclosureButton"})}}}),V=S({name:"DisclosurePanel",props:{as:{type:[Object,String],default:"div"},static:{type:Boolean,default:!1},unmount:{type:Boolean,default:!0},id:{type:String,default:()=>`headlessui-disclosure-panel-${O()}`}},setup(l,{attrs:u,slots:r,expose:o}){let e=g("DisclosurePanel");I(()=>{e.panelId.value=l.id}),B(()=>{e.panelId.value=null}),o({el:e.panel,$el:e.panel}),h(E,e.panelId);let i=T(),s=D(()=>i!==null?(i.value&m.Open)===m.Open:e.disclosureState.value===0);return()=>{let t={open:e.disclosureState.value===0,close:e.close},{id:n,...p}=l,f={id:n,ref:e.panel};return y({ourProps:f,theirProps:p,slot:t,attrs:u,slots:r,features:k.RenderStrategy|k.Static,visible:s.value,name:"DisclosurePanel"})}}});export{R as Q,U as V,V as X};
